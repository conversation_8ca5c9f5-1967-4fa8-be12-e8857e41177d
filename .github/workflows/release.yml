name: Release
on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
jobs:
  release:
    runs-on: ubuntu-latest
    timeout-minutes: 240
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Check version
        run: |
          VERSION_TAG=${{ github.ref_name }}
          VERSION_TAG=${VERSION_TAG#v}
          PROJECT_VERSION=$(grep "^version=" gradle.properties | cut -d'=' -f2)
          if [[ "$PROJECT_VERSION" == "${VERSION_TAG}-SNAPSHOT" ]]; then
            echo "Version match: tag $VERSION_TAG matches project version $PROJECT_VERSION, proceeding with release"
          else
            echo "Version mismatch: tag $VERSION_TAG does not match project version $PROJECT_VERSION"
            exit 1
          fi

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build
        run: ./gradlew build --no-daemon

      - name: Release
        env:
          MAVENCENTRAL_USERNAME: ${{ secrets.MAVENCENTRAL_USERNAME }}
          MAVENCENTRAL_PASSWORD: ${{ secrets.MAVENCENTRAL_PASSWORD }}
          GPG_PUBLIC_KEY: ${{ secrets.GPG_PUBLIC_KEY }}
          GPG_SECRET_KEY: ${{ secrets.GPG_SECRET_KEY }}
          GPG_PASSPHRASE: ${{ secrets.GPG_PASSPHRASE }}
        run: |
          tmpfile=$(mktemp)
          sed 's/^\(version=.*\)-SNAPSHOT/\1/' gradle.properties > "$tmpfile" && mv "$tmpfile" gradle.properties
          ./gradlew publish jreleaserDeploy --no-daemon --stacktrace
