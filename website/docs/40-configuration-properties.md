---
sidebar_position: 40
---

# Configuration Properties

Configuration properties for the httpexchange-spring-boot-starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).

## Table of Contents
* [**httpexchange-spring-boot-autoconfigure**](#httpexchange-spring-boot-autoconfigure)
  * [**http-exchange** - `io.github.danielliu1123.httpexchange.HttpExchangeProperties`](#http-exchange)

  * [**http-exchange.refresh** - `io.github.danielliu1123.httpexchange.HttpExchangeProperties$Refresh`](#http-exchange.refresh)

## httpexchange-spring-boot-autoconfigure
### http-exchange
**Class:** `io.github.danielliu1123.httpexchange.HttpExchangeProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| base-packages| java.util.Set&lt;java.lang.String&gt;| | | | 
| base-url| java.lang.String| | | | 
| bean-to-query-enabled| java.lang.Boolean| | | | 
| channels| java.util.List&lt;io.github.danielliu1123.httpexchange.HttpExchangeProperties$Channel&gt;| | | | 
| client-type| io.github.danielliu1123.httpexchange.HttpExchangeProperties$ClientType| | | | 
| clients| java.util.Set&lt;java.lang.Class&lt;?&gt;&gt;| | | | 
| enabled| java.lang.Boolean| | | | 
| headers| java.util.List&lt;io.github.danielliu1123.httpexchange.HttpExchangeProperties$Header&gt;| | | | 
| http-client-reuse-enabled| java.lang.Boolean| | | | 
| loadbalancer-enabled| java.lang.Boolean| | | | 
| request-mapping-support-enabled| java.lang.Boolean| | | | 
| warn-unused-config-enabled| java.lang.Boolean| | | | 
### http-exchange.refresh
**Class:** `io.github.danielliu1123.httpexchange.HttpExchangeProperties$Refresh`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 

